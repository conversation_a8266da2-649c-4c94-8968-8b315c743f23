import { useState, useEffect } from 'react';
import { Menu, X, ChevronDown, Mail, Volume2, VolumeX } from 'lucide-react';

export default function PortfolioSite() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const [isMuted, setIsMuted] = useState(true);

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }
    setIsMenuOpen(false);
  };

  // Calculate video transform based on scroll
  const videoScale = Math.max(0, 1 - scrollY / 800);
  const videoOpacity = Math.max(0, 1 - scrollY / 600);

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white/90 backdrop-blur-md shadow-lg fixed w-full top-0 z-50 border-b border-white/20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Suzanne De Silva
            </div>
            
            {/* Desktop Menu */}
            <div className="hidden lg:flex items-center space-x-1 bg-white/60 backdrop-blur-sm rounded-full px-2 py-2 shadow-lg border border-white/30">
              <button onClick={() => scrollToSection('home')} className="px-4 py-2 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-purple-500 hover:to-blue-500 rounded-full transition-all duration-300 font-medium relative group">
                Home
              </button>
              <button onClick={() => scrollToSection('about')} className="px-4 py-2 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-purple-500 hover:to-blue-500 rounded-full transition-all duration-300 font-medium relative group">
                About
              </button>
              <button onClick={() => scrollToSection('product-mgmt')} className="px-4 py-2 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-emerald-500 hover:to-teal-500 rounded-full transition-all duration-300 font-medium relative group">
                Product Management
              </button>
              <button onClick={() => scrollToSection('product-mkt')} className="px-4 py-2 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 rounded-full transition-all duration-300 font-medium relative group">
                Product Marketing
              </button>
              <button onClick={() => scrollToSection('gallery')} className="px-4 py-2 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-blue-500 hover:to-indigo-500 rounded-full transition-all duration-300 font-medium relative group">
                Gallery
              </button>
              <button onClick={() => scrollToSection('experience')} className="px-4 py-2 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-orange-500 hover:to-red-500 rounded-full transition-all duration-300 font-medium relative group">
                Experience
              </button>
              <button onClick={() => scrollToSection('contact')} className="px-4 py-2 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-cyan-500 hover:to-blue-500 rounded-full transition-all duration-300 font-medium relative group">
                Contact
              </button>
            </div>

            {/* Mobile Menu Button */}
            <button 
              className="lg:hidden p-3 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg hover:from-purple-400 hover:to-blue-400 transition-all duration-300"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="lg:hidden pb-4 bg-white/95 backdrop-blur-md rounded-2xl mt-4 border border-white/30 shadow-2xl mx-4 mb-4">
              <div className="p-2 space-y-2">
                <button onClick={() => scrollToSection('home')} className="block w-full text-left py-3 px-4 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-purple-500 hover:to-blue-500 rounded-xl transition-all duration-300 font-medium">Home</button>
                <button onClick={() => scrollToSection('about')} className="block w-full text-left py-3 px-4 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-purple-500 hover:to-blue-500 rounded-xl transition-all duration-300 font-medium">About</button>
                <button onClick={() => scrollToSection('product-mgmt')} className="block w-full text-left py-3 px-4 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-emerald-500 hover:to-teal-500 rounded-xl transition-all duration-300 font-medium">Product Management</button>
                <button onClick={() => scrollToSection('product-mkt')} className="block w-full text-left py-3 px-4 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 rounded-xl transition-all duration-300 font-medium">Product Marketing</button>
                <button onClick={() => scrollToSection('gallery')} className="block w-full text-left py-3 px-4 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-blue-500 hover:to-indigo-500 rounded-xl transition-all duration-300 font-medium">Gallery</button>
                <button onClick={() => scrollToSection('experience')} className="block w-full text-left py-3 px-4 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-orange-500 hover:to-red-500 rounded-xl transition-all duration-300 font-medium">Experience</button>
                <button onClick={() => scrollToSection('contact')} className="block w-full text-left py-3 px-4 text-gray-700 hover:text-white hover:bg-gradient-to-r hover:from-cyan-500 hover:to-blue-500 rounded-xl transition-all duration-300 font-medium">Contact</button>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section id="home" className="pt-24 pb-16 bg-gray-900 relative overflow-hidden min-h-screen flex items-center">
        {/* Video Background */}
        <div
          className="absolute inset-0 z-0"
          style={{
            transform: `scale(${videoScale})`,
            opacity: videoOpacity,
            transition: 'transform 0.1s ease-out, opacity 0.1s ease-out'
          }}
        >
          <video
            autoPlay
            muted={isMuted}
            loop
            playsInline
            className="w-full h-full object-cover"
          >
            <source src="/videos/s10-feature-suzanne-desilva.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
          <div className="absolute inset-0 bg-gray-900/60"></div>
        </div>

        {/* Sound Control Button */}
        <button
          onClick={() => setIsMuted(!isMuted)}
          className="absolute top-6 right-6 z-30 bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white rounded-lg p-3 transition-all duration-300 shadow-sm border border-white/20"
          aria-label={isMuted ? "Unmute video" : "Mute video"}
        >
          {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
        </button>

        
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-20 h-full flex flex-col justify-end pb-16">
          <div className="flex justify-end">
            <div className="text-right">
              <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">Suzanne De Silva</h1>
              <p className="text-xl text-gray-200 max-w-3xl ml-auto leading-relaxed mb-8">
                Product Management & Marketing Leader | Technology Innovation Expert |
                Driving Consumer-Centric Solutions in 5G, IoT, and AI
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* About Section - Moved to first position */}
      <section id="about" className="py-16 bg-white relative">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-6">About Me</h2>
              <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                I am passionate about developing technology that addresses real consumer needs; 
                makes life easier, and reduces technology adoption friction through the use of AI.
              </p>
              <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                There has never been a more exciting time to be in technology. With the launch of 5G networks 
                and their hyper speeds, the proliferation of IoT, the rise of voice as interface, to the 
                simplification of tech with AI, this foundational tech is driving rapid change in the industry.
              </p>
              <p className="text-lg text-gray-700 leading-relaxed mb-8">
                I am excited to be at the heart of this shift, leveraging technology to help consumers 
                experience and do amazing things.
              </p>
              
              {/* Navigation buttons to new sections */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <button 
                  onClick={() => scrollToSection('product-mgmt')}
                  className="group bg-gradient-to-r from-emerald-500 to-teal-600 text-white rounded-xl p-4 text-center hover:from-emerald-400 hover:to-teal-500 transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                  <div className="font-semibold mb-1">Product Management</div>
                  <div className="text-sm opacity-90">Philosophy & Approach</div>
                </button>
                <button 
                  onClick={() => scrollToSection('product-mkt')}
                  className="group bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-xl p-4 text-center hover:from-purple-400 hover:to-pink-500 transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                  <div className="font-semibold mb-1">Product Marketing</div>
                  <div className="text-sm opacity-90">Strategy & Positioning</div>
                </button>
                <button 
                  onClick={() => scrollToSection('gallery')}
                  className="group bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-xl p-4 text-center hover:from-blue-400 hover:to-indigo-500 transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                  <div className="font-semibold mb-1">Media Gallery</div>
                  <div className="text-sm opacity-90">Videos & Presentations</div>
                </button>
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-8 shadow-lg">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Key Expertise</h3>
              <ul className="space-y-4">
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-gray-400 rounded-full mr-4"></div>
                  <span className="text-gray-700 font-medium">Product Development & Launch</span>
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-gray-400 rounded-full mr-4"></div>
                  <span className="text-gray-700 font-medium">Consumer Insights Research</span>
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-gray-400 rounded-full mr-4"></div>
                  <span className="text-gray-700 font-medium">Hardware & Software Stack</span>
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-gray-400 rounded-full mr-4"></div>
                  <span className="text-gray-700 font-medium">AI & Technology Integration</span>
                </li>
                <li className="flex items-center">
                  <div className="w-2 h-2 bg-gray-400 rounded-full mr-4"></div>
                  <span className="text-gray-700 font-medium">5G & IoT Innovation</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Experience Highlights */}
      <section id="experience" className="py-16 bg-gray-50 relative overflow-hidden">

        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <h2 className="text-4xl font-bold text-gray-900 text-center mb-12">Experience Highlights</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="text-4xl font-bold text-gray-900 mb-3">16+</div>
              <div className="text-xl font-semibold text-gray-700 mb-3">Years Experience</div>
              <p className="text-gray-600">
                Developing and launching innovative products across the technology spectrum.
              </p>
            </div>
            <div className="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="text-4xl font-bold text-gray-900 mb-3">Millions</div>
              <div className="text-xl font-semibold text-gray-700 mb-3">Global Consumers</div>
              <p className="text-gray-600">
                Products have driven engagement and delight across millions of users worldwide.
              </p>
            </div>
            <div className="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="text-4xl font-bold text-gray-900 mb-3">30+</div>
              <div className="text-xl font-semibold text-gray-700 mb-3">Product Launches</div>
              <p className="text-gray-600">
                Successfully launched diverse hardware and software products.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Technology Focus Areas */}
      <section className="py-16 bg-white relative">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <h2 className="text-4xl font-bold text-gray-900 text-center mb-12">Technology Focus Areas</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="group text-center p-6 bg-gray-50 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                <div className="w-6 h-6 bg-gray-400 rounded-full"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">5G Networks</h3>
              <p className="text-gray-600 text-sm">Hyper-speed connectivity driving innovation</p>
            </div>
            <div className="group text-center p-6 bg-gray-50 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                <div className="w-6 h-6 bg-gray-400 rounded-full"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">IoT</h3>
              <p className="text-gray-600 text-sm">Connected devices transforming experiences</p>
            </div>
            <div className="group text-center p-6 bg-gray-50 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                <div className="w-6 h-6 bg-gray-400 rounded-full"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Voice Interface</h3>
              <p className="text-gray-600 text-sm">Natural interaction through voice technology</p>
            </div>
            <div className="group text-center p-6 bg-gray-50 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
              <div className="w-12 h-12 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
                <div className="w-6 h-6 bg-gray-400 rounded-full"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">AI Simplification</h3>
              <p className="text-gray-600 text-sm">Making technology more accessible with AI</p>
            </div>
          </div>
        </div>
      </section>

      {/* Product Management Philosophy Section */}
      <section id="product-mgmt" className="py-16 bg-gray-50 relative">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Product Management Philosophy</h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto">My approach is simple and revolves around 4 key themes</p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <div className="bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl p-8 text-white shadow-xl">
              <h3 className="text-2xl font-bold mb-4">Core Principles</h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-yellow-300 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>Launch and learn quickly</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-yellow-300 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>Keep the end consumer in mind</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-yellow-300 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>Minimize the number of unknowns</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-yellow-300 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>Create a feedback loop of learning and best practices</span>
                </li>
              </ul>
            </div>
            
            <div className="space-y-6">
              <div className="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow">
                <h4 className="text-lg font-semibold text-emerald-600 mb-2">Be Proactive</h4>
                <p className="text-gray-700">As I go into new projects I always advise my team that "we don't know what we don't know". So I challenge everyone to ask questions, uncover insights, and learn quickly.</p>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow">
                <h4 className="text-lg font-semibold text-teal-600 mb-2">Start with the Future in Mind</h4>
                <p className="text-gray-700">I take a software development architecture approach to everything I do - build a scalable framework/platform that can stand the test of time and be added to.</p>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl p-6 text-white text-center transform hover:scale-105 transition-transform">
              <h4 className="text-lg font-semibold mb-3">Prototype and Fail Fast</h4>
              <p className="text-sm">Demonstrate functionality along the way to ensure no surprises in the final product.</p>
            </div>
            
            <div className="bg-gradient-to-br from-teal-500 to-emerald-600 rounded-xl p-6 text-white text-center transform hover:scale-105 transition-transform">
              <h4 className="text-lg font-semibold mb-3">Budget for the Unknown</h4>
              <p className="text-sm">Avoid failure by leaving enough room and time for the unexpected. Nothing is worse than missed deadlines.</p>
            </div>
            
            <div className="bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl p-6 text-white text-center transform hover:scale-105 transition-transform">
              <h4 className="text-lg font-semibold mb-3">Build Loyalty into the Product</h4>
              <p className="text-sm">Loyalty starts with setting expectations and delivering everyday positive experiences.</p>
            </div>
          </div>

          <div className="mt-12 text-center">
            <div className="bg-white rounded-2xl p-8 shadow-xl inline-block">
              <h4 className="text-xl font-semibold text-gray-900 mb-4">Monitor, Hypothesize, Test</h4>
              <p className="text-gray-700 mb-6 max-w-2xl">When a product is in market, collect data about user behavior and pair it with hypotheses for actionable insights.</p>
              <a 
                href="https://suzannedesilva.com/wp-content/uploads/2023/11/Product-Management-20-Years-in-20-Minutes-1.pdf"
                target="_blank"
                rel="noopener noreferrer" 
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-500 to-cyan-500 text-white rounded-full font-semibold hover:from-emerald-400 hover:to-cyan-400 transition-all duration-300 transform hover:scale-105"
              >
                Download Presentation
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Product Marketing Philosophy Section */}
      <section id="product-mkt" className="py-16 bg-gradient-to-br from-violet-900 via-purple-900 to-indigo-900 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-violet-600/20 to-indigo-600/20"></div>
        <div className="absolute top-20 left-20 w-64 h-64 bg-gradient-to-r from-pink-500 to-violet-600 rounded-full mix-blend-screen filter blur-3xl opacity-20"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-cyan-400 to-blue-600 rounded-full mix-blend-screen filter blur-3xl opacity-20"></div>
        
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold bg-gradient-to-r from-yellow-300 to-pink-300 bg-clip-text text-transparent mb-4">Product Marketing Philosophy</h2>
            <p className="text-xl text-gray-200 max-w-4xl mx-auto">Strategically determining the 10 to 20 words that uniquely positions, acquires, sustains and defends a product's share in the market.</p>
          </div>

          <div className="mb-12">
            <div className="bg-gradient-to-br from-violet-600 to-purple-700 rounded-2xl p-8 mb-8">
              <h3 className="text-2xl font-bold mb-4 text-yellow-300">What is Product Marketing?</h3>
              <p className="text-white/90 leading-relaxed">Product marketing is the process of strategically determining the 10 to 20 words that uniquely positions a product in the market. These words fit into a framework called the value proposition that provides the lens through which the product comes to life. A lot of insights and analysis go into determining the who (to target), the what (which features to talk about), and the how (to talk about those features).</p>
            </div>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <div className="bg-gradient-to-br from-pink-500 to-rose-600 rounded-xl p-6 text-center transform hover:scale-105 transition-transform">
              <h4 className="text-lg font-semibold mb-3">Anchor New Technology in the Familiar</h4>
              <p className="text-sm text-white/90">Help consumers make the psychological leap of understanding how new technology fits into their lives.</p>
            </div>
            
            <div className="bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl p-6 text-center transform hover:scale-105 transition-transform">
              <h4 className="text-lg font-semibold mb-3">Create a Platform</h4>
              <p className="text-sm text-white/90">Build positioning on a platform that can scale over time, ensuring your product uniquely stands for something.</p>
            </div>
            
            <div className="bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl p-6 text-center transform hover:scale-105 transition-transform">
              <h4 className="text-lg font-semibold mb-3">Position to Lockout Competition</h4>
              <p className="text-sm text-white/90">Position in competitive white spaces authentic to your strengths and away from competitors.</p>
            </div>
            
            <div className="bg-gradient-to-br from-orange-500 to-red-600 rounded-xl p-6 text-center transform hover:scale-105 transition-transform">
              <h4 className="text-lg font-semibold mb-3">Marketize Technology</h4>
              <p className="text-sm text-white/90">Brand technology around key user benefits, not the literal name of the technology.</p>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
              <h4 className="text-xl font-semibold mb-4 text-yellow-300">5G Example: "Everything You Love, Hyperfast"</h4>
              <p className="text-white/90 mb-4">Introducing 5G by anchoring it in familiar smartphone experiences rather than technical specifications.</p>
              <div className="bg-black/20 rounded-lg p-4">
                <p className="text-sm text-cyan-300">View the campaign: "Introducing 5G - Everything You Love, Hyperfast"</p>
              </div>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
              <h4 className="text-xl font-semibold mb-4 text-yellow-300">Galaxy Foundation of Features</h4>
              <p className="text-white/90 mb-4">Creating a positioning platform that ensures consumers view the product through a uniquely crafted lens over time.</p>
              <div className="bg-black/20 rounded-lg p-4">
                <p className="text-sm text-cyan-300">"The Performance You Can Expect In Galaxy"</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Media Gallery Section */}
      <section id="gallery" className="py-16 bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50 relative">
        <div className="absolute inset-0 bg-gradient-to-r from-slate-100/30 to-zinc-100/30"></div>
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold bg-gradient-to-r from-slate-600 to-zinc-600 bg-clip-text text-transparent mb-4">Media Gallery</h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto">Featured presentations and media appearances</p>
          </div>
          
          <div className="grid md:grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden transform hover:scale-105 transition-transform">
              <div className="aspect-video bg-gradient-to-br from-blue-600 to-purple-700 relative">
                <div className="absolute inset-0 flex items-center justify-center">
                  <a
                    href="https://www.youtube.com/watch?v=USDvd7fhbd0"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-white/20 backdrop-blur-sm rounded-full p-4 hover:bg-white/30 transition-colors"
                  >
                    <svg className="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </a>
                </div>
                <div className="absolute bottom-4 left-4 right-4">
                  <h3 className="text-white font-semibold text-lg drop-shadow">Galaxy S10 Features by Suzanne De Silva</h3>
                  <p className="text-white/90 text-sm drop-shadow">Galaxy Unpacked 2019 - Official Samsung Presentation</p>
                </div>
              </div>
              <div className="p-6">
                <p className="text-gray-700">Watch Suzanne De Silva present the revolutionary Galaxy S10 features at Samsung's Galaxy Unpacked 2019 event.</p>
              </div>
            </div>
            
            <div className="bg-white rounded-2xl shadow-xl p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Speaking Engagements</h3>
              <div className="space-y-4">
                <div className="border-l-4 border-purple-500 pl-4 bg-purple-50 p-4 rounded-r-lg">
                  <h4 className="font-semibold text-gray-900">Galaxy S10 Unpacked 2019</h4>
                  <p className="text-gray-600">Featured presenter for Galaxy S10 launch event showcasing revolutionary features</p>
                  <p className="text-purple-600 text-sm font-medium mt-1">• Infinity-O Display Technology • Camera Innovation • 5G Capabilities</p>
                </div>
                <div className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-semibold text-gray-900">Samsung Unpacked Events</h4>
                  <p className="text-gray-600">Product launches and strategy presentations for Galaxy devices</p>
                </div>
                <div className="border-l-4 border-green-500 pl-4">
                  <h4 className="font-semibold text-gray-900">Industry Conferences</h4>
                  <p className="text-gray-600">Keynotes on mobile innovation and product management</p>
                </div>
                <div className="border-l-4 border-orange-500 pl-4">
                  <h4 className="font-semibold text-gray-900">Media Interviews</h4>
                  <p className="text-gray-600">Technology insights and market analysis</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-16 bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-cyan-600/20"></div>
        <div className="absolute top-10 right-10 w-72 h-72 bg-gradient-to-r from-pink-500 to-violet-600 rounded-full mix-blend-screen filter blur-3xl opacity-30 animate-pulse"></div>
        <div className="absolute bottom-10 left-10 w-96 h-96 bg-gradient-to-r from-cyan-400 to-blue-600 rounded-full mix-blend-screen filter blur-3xl opacity-30 animate-pulse delay-75"></div>
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <h2 className="text-4xl font-bold mb-6 bg-gradient-to-r from-cyan-300 to-purple-300 bg-clip-text text-transparent">Let's Connect</h2>
          <p className="text-xl text-gray-200 mb-8 max-w-2xl mx-auto">
            Interested in collaborating on innovative technology solutions? 
            Let's discuss how we can create amazing consumer experiences together.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="mailto:<EMAIL>" 
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-cyan-500 to-blue-600 text-white rounded-full font-semibold hover:from-cyan-400 hover:to-blue-500 transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              <Mail className="mr-2" size={20} />
              Get In Touch
            </a>
            <a 
              href="https://linkedin.com/in/suzannedesilva" 
              className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full font-semibold hover:from-purple-400 hover:to-pink-400 transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              View LinkedIn
            </a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-gray-400">
              © 2025 Suzanne De Silva. Mobile Expert, Creative Thinker, Inspiring Leader.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}